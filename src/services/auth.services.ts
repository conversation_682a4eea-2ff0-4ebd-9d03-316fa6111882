import env from "@/env";

export type User = {
  id: string;
  name: string;
  email: string;
  password: string;
};

export async function getUsers(params: Partial<User>): Promise<User[] | []> {
  const query = new URLSearchParams(params).toString();
  const response = await fetch(
    env.EXPO_PUBLIC_API_URL + "/users" + "?" + query
  );
  return response.json();
}

export async function getUserById(id: string): Promise<User | null> {
  const response = await fetch(env.EXPO_PUBLIC_API_URL + "/users/" + id);
  return response.json();
}

export async function createUser(user: Omit<User, "id">): Promise<User> {
  const response = await fetch(env.EXPO_PUBLIC_API_URL + "/users", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(user),
  });
  return response.json();
}

export async function updateUser(user: Partial<User>): Promise<User> {
  const response = await fetch(env.EXPO_PUBLIC_API_URL + "/users/" + user.id, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(user),
  });
  return response.json();
}
