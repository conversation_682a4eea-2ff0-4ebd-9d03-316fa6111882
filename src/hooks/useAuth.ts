import {
  createUser,
  getUserById,
  getUsers,
  type User,
} from "@/services/auth.services";
import { useAuth } from "@/store/auth.state";
import { loginSchema, registerSchema } from "@/utils/auth.validation";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useRouter } from "expo-router";

export const useAuthLogin = () => {
  const { setUser } = useAuth();
  const router = useRouter();

  return useMutation({
    mutationFn: async (user: Pick<User, "email" | "password">) => {
      // do input validation
      await loginSchema.parseAsync(user);

      // check if user exists (?email&password), throw error if not
      const users = await getUsers({
        email: user.email,
        password: user.password,
      });
      if (users.length === 0) {
        throw new Error("User does not exist");
      }

      // if all good, return user
      return {
        id: users[0].id,
        name: users[0].name,
        email: users[0].email,
      };
    },
    onSuccess: (data) => {
      setUser(data);
      router.replace("/");
    },
    onError: (error) => {
      console.log(error);
    },
  });
};

export const useAuthRegister = () => {
  const { setUser } = useAuth();
  const router = useRouter();

  return useMutation({
    mutationFn: async (
      user: Omit<User, "id"> & { confirmPassword: string }
    ) => {
      const { confirmPassword, ...rest } = user;
      if (rest.password !== confirmPassword) {
        throw new Error("Passwords do not match");
      }

      // do input validation
      // remove confirmPassword from user

      const validatedUser = await registerSchema.parseAsync(rest);

      // check if user exists (?email), throw error if so
      const users = await getUsers({
        email: user.email,
      });
      if (users.length > 0) {
        throw new Error("User already exists");
      }

      // if all good, create user
      const newUser = await createUser(validatedUser);
      return {
        id: newUser.id,
        name: newUser.name,
        email: newUser.email,
      };
    },
    onSuccess: (data) => {
      setUser(data);
      router.replace("/");
    },
    onError: (error) => {
      console.log(error);
    },
  });
};

export const useCurrentUser = () => {
  const { id } = useAuth();

  return useQuery({
    queryKey: ["user", id],
    queryFn: async () => {
      if (!id) {
        return null;
      }
      return await getUserById(id);
    },
    enabled: !!id,
  });
};
