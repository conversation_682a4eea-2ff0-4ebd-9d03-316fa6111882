import { useAuth } from "@/store/auth.state";
import { View } from "react-native";
import { Button } from "react-native-paper";

export default function Profile() {
  const { clearUser } = useAuth();
  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Button mode="contained" icon="logout" onPress={() => clearUser()}>
        Logout
      </Button>
    </View>
  );
}
