import { useAuth } from "@/store/auth.state";
import IonIcons from "@expo/vector-icons/Ionicons";
import { Redirect, Tabs } from "expo-router";

export default function MainLayout() {
  const { id } = useAuth();

  if (!id) {
    return <Redirect href="/login" />;
  }

  return (
    <Tabs screenOptions={{ tabBarActiveTintColor: "purple" }}>
      <Tabs.Screen
        name="(home)/index"
        options={{
          title: "Home",
          tabBarIcon: ({ color }) => (
            <IonIcons name="home" color={color} size={24} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile/index"
        options={{
          title: "Profile",
          tabBarIcon: ({ color }) => (
            <IonIcons name="person" color={color} size={24} />
          ),
        }}
      />
    </Tabs>
  );
}
