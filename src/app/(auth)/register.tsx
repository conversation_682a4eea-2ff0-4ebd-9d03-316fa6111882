import { useAuthRegister } from "@/hooks/useAuth";
import { useState } from "react";
import { View } from "react-native";
import { Button, HelperText, Text, TextInput } from "react-native-paper";

export default function Register() {
  const [visible, setVisible] = useState(false);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const { mutate: register, error, isPending } = useAuthRegister();

  return (
    <View
      style={{
        flex: 1,
        gap: 16,
        padding: 16,
      }}
    >
      <Text style={{ fontSize: 24, fontWeight: "bold" }}>Register</Text>
      <TextInput
        label="Name"
        mode="outlined"
        autoCapitalize="none"
        value={name}
        onChangeText={(text) => {
          setName(text);
        }}
        disabled={isPending}
      />
      <TextInput
        label="Email"
        mode="outlined"
        autoCapitalize="none"
        value={email}
        onChangeText={(text) => {
          setEmail(text);
        }}
        disabled={isPending}
      />
      <TextInput
        label="Password"
        mode="outlined"
        secureTextEntry={!visible}
        value={password}
        onChangeText={(text) => {
          setPassword(text);
        }}
        disabled={isPending}
        right={
          <TextInput.Icon
            icon={visible ? "eye-off" : "eye"}
            onPress={() => setVisible(!visible)}
          />
        }
      />
      <TextInput
        label="Confirm Password"
        mode="outlined"
        secureTextEntry={!visible}
        value={confirmPassword}
        onChangeText={(text) => {
          setConfirmPassword(text);
        }}
        disabled={isPending}
        right={
          <TextInput.Icon
            icon={visible ? "eye-off" : "eye"}
            onPress={() => setVisible(!visible)}
          />
        }
      />
      <HelperText type="error" visible={!!error}>
        {error?.message}
      </HelperText>
      <Button
        mode="contained"
        icon="login"
        onPress={() =>
          register({
            name,
            email,
            password,
            confirmPassword,
          })
        }
        loading={isPending}
        disabled={isPending}
      >
        Register
      </Button>
    </View>
  );
}
