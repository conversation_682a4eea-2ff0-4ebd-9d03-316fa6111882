import { useAuthLogin } from "@/hooks/useAuth";
import { Link } from "expo-router";
import { useState } from "react";
import { View } from "react-native";
import { Button, HelperText, Text, TextInput } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [visible, setVisible] = useState(false);
  const { mutate: login, error, isPending } = useAuthLogin();

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View
        style={{
          flex: 1,
          gap: 16,
          padding: 16,
        }}
      >
        <Text style={{ fontSize: 24, fontWeight: "bold" }}>Login</Text>
        <TextInput
          label="Email"
          mode="outlined"
          value={email}
          autoCapitalize="none"
          onChangeText={(text) => {
            setEmail(text);
          }}
          disabled={isPending}
        />
        <TextInput
          label="Password"
          mode="outlined"
          secureTextEntry={!visible}
          value={password}
          onChangeText={(text) => {
            setPassword(text);
          }}
          disabled={isPending}
          right={
            <TextInput.Icon
              icon={visible ? "eye-off" : "eye"}
              onPress={() => setVisible(!visible)}
            />
          }
        />
        <HelperText type="error" visible={!!error}>
          {error?.message}
        </HelperText>
        <Button
          mode="contained"
          icon="login"
          onPress={() => login({ email, password })}
          loading={isPending}
          disabled={isPending}
        >
          Login
        </Button>
        <Text>
          Don't have an account?
          <Link href="/register"> Register</Link>
        </Text>
      </View>
    </SafeAreaView>
  );
}
