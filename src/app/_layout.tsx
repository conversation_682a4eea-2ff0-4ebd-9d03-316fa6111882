import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Stack } from "expo-router";
import { SafeAreaProvider } from "react-native-safe-area-context";

const queryClient = new QueryClient();
export default function RootLayout() {
  return (
    <QueryClientProvider client={queryClient}>
      <SafeAreaProvider>
        <Stack>
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen
            name="(auth)/login"
            options={{ headerShown: false, title: "Login" }}
          />
          <Stack.Screen
            name="(auth)/register"
            options={{ headerShown: true, title: "Register" }}
          />
        </Stack>
      </SafeAreaProvider>
    </QueryClientProvider>
  );
}
